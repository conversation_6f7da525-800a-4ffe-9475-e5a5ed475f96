# FusionPBX Docker - Git Ignore File
# Author: Finstar Team

# Runtime Data Directories
data/
dev-data/
backups/

# Cookie Files
cookies*.txt
*.cookie

# Temporary Files
*.tmp
*.temp
*.log
*.pid
*.swp
*.swo
*~

# Build Results
*-results.txt
build-*.log

# Environment Files (keep .env.example)
.env.local
.env.*.local

# IDE Files
.vscode/
.idea/
*.sublime-*

# OS Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.docker/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (if any)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/

# SSL Certificates (keep examples)
*.pem
*.key
*.crt
*.csr
!*.example.*

# Database dumps
*.sql
*.dump

# Backup files
*.bak
*.backup

# Test files
test-*.txt
debug-*.txt
setup-*.php

# Local configuration overrides
docker-compose.override.yml
.env.override
