version: '3.8'

services:
  fusionpbx:
    image: ${FUSIONPBX_IMAGE:-skytruongdev/fusionpbx:latest}
    container_name: fusionpbx
    restart: unless-stopped
    ports:
      - "8080:80"          # HTTP Web Interface
      - "8443:443"         # HTTPS Web Interface
      - "5060:5060/tcp"    # SIP TCP
      - "5060:5060/udp"    # SIP UDP
      - "5080:5080/tcp"    # SIP Alternative TCP
      - "5080:5080/udp"    # SIP Alternative UDP
      - "8021:8021"        # FreeSWITCH Event Socket
      - "10000-10100:10000-10100/udp"  # RTP Media
    volumes:
      - fusionpbx-data:/var/lib/postgresql
      - fusionpbx-config:/etc/fusionpbx
      - fusionpbx-recordings:/usr/local/freeswitch/recordings
      - fusionpbx-logs:/var/log
      - fusionpbx-sounds:/usr/local/freeswitch/sounds
      - fusionpbx-storage:/usr/local/freeswitch/storage
    environment:
      - TZ=Asia/Ho_<PERSON>_Minh
      - FUSIONPBX_DOMAIN=${FUSIONPBX_DOMAIN:-pbx.finstar.vn}
      - FUSIONPBX_ADMIN_USER=${FUSIONPBX_ADMIN_USER:-admin}
      - FUSIONPBX_ADMIN_PASSWORD=${FUSIONPBX_ADMIN_PASSWORD:-Finstar@2025}
      - FUSIONPBX_SETUP_WIZARD=${FUSIONPBX_SETUP_WIZARD:-false}  # Disable setup wizard for production
      - AUTO_INSTALL=${AUTO_INSTALL:-true}  # Enable auto-install for production
      - ENABLE_HTTPS=${ENABLE_HTTPS:-true}
      - ENABLE_FAIL2BAN=${ENABLE_FAIL2BAN:-true}
      - DB_HOST=${DB_HOST:-localhost}
      - DB_PORT=${DB_PORT:-5432}
      - DB_NAME=${DB_NAME:-fusionpbx}
      - DB_USER=${DB_USER:-fusionpbx}
      - DB_PASSWORD=${DB_PASSWORD:-fusionpbx}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s  # Increased for auto-install time

    labels:
      - "com.docker.compose.project=fusionpbx-production"
      - "com.docker.compose.service=fusionpbx"
      - "com.finstar.environment=production"
      - "com.finstar.version=1.0"



volumes:
  fusionpbx-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/fusionpbx/data
  fusionpbx-config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/fusionpbx/config
  fusionpbx-recordings:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/fusionpbx/recordings
  fusionpbx-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/fusionpbx/logs
  fusionpbx-sounds:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/fusionpbx/sounds
  fusionpbx-storage:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/fusionpbx/storage
