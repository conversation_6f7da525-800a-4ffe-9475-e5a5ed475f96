[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:postgresql]
command=/usr/lib/postgresql/15/bin/postgres -D /var/lib/postgresql/15/main -c config_file=/etc/postgresql/15/main/postgresql.conf
user=postgres
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/postgresql.log
priority=100

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/nginx.log
priority=200

[program:php-fpm]
command=/usr/sbin/php-fpm8.2 -F
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/php-fpm.log
priority=300

[program:freeswitch]
command=/usr/local/freeswitch/bin/freeswitch -nc -nf
user=fusionpbx
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/freeswitch.log
priority=400

[program:snmpd]
command=/usr/sbin/snmpd -f -Lo
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/snmpd.log
priority=500

[program:fail2ban]
command=/usr/bin/fail2ban-server -f
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/fail2ban.log
priority=600
