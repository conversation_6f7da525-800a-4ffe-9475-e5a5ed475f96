#database details
database.0.type = pgsql
database.0.host = {database_host}
database.0.port = 5432
database.0.sslmode = prefer
database.0.name = {database_name}
database.0.username = {database_username}
database.0.password = {database_password}

#cache details
cache.method = file
cache.location = /var/cache/fusionpbx
cache.settings = true

#email details
email.method = smtp
email.smtp_host = localhost
email.smtp_port = 25
email.smtp_secure = none
email.smtp_auth = false
email.smtp_username = 
email.smtp_password = 
email.smtp_from = 
email.smtp_from_name = 

#event socket details
event.socket.host = 127.0.0.1
event.socket.port = 8021
event.socket.password = ClueCon

#switch details
switch.conf.dir = /etc/freeswitch
switch.log.dir = /var/log/freeswitch
switch.db.dir = /var/lib/freeswitch/db
switch.sounds.dir = /usr/local/freeswitch/sounds
switch.recordings.dir = /usr/local/freeswitch/recordings
switch.storage.dir = /usr/local/freeswitch/storage
switch.voicemail.dir = /usr/local/freeswitch/storage/voicemail
switch.scripts.dir = /usr/local/freeswitch/scripts
switch.grammar.dir = /usr/local/freeswitch/grammar
switch.bin.dir = /usr/local/freeswitch/bin
