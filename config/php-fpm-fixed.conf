[fusionpbx]
user = www-data
group = www-data

listen = /var/run/php/fusionpbx-fpm.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

; PHP configuration
php_admin_value[sendmail_path] = /usr/sbin/sendmail -t -i -f www-data@localhost
php_flag[display_errors] = off
php_admin_value[error_log] = /var/log/php/fpm-error.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 512M
php_admin_value[upload_max_filesize] = 80M
php_admin_value[post_max_size] = 80M
php_admin_value[max_execution_time] = 600
php_admin_value[max_input_time] = 600

; Session configuration
php_value[session.save_handler] = files
php_value[session.save_path] = /var/lib/php/sessions
php_value[session.gc_probability] = 1
php_value[session.gc_divisor] = 1000
php_value[session.gc_maxlifetime] = 1440

; Security - Fixed open_basedir and enabled shell_exec
php_admin_value[open_basedir] = /var/www/fusionpbx:/tmp:/var/tmp:/usr/local/freeswitch:/etc/fusionpbx:/usr/local/etc:/proc/sys/kernel/random:/ProgramData/fusionpbx
php_admin_value[disable_functions] = passthru,proc_open,popen
