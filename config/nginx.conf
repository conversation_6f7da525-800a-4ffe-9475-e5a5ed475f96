server {
    listen 80;
    listen [::]:80;
    server_name _;

    root /var/www/fusionpbx;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # PHP handling
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }

    # Static files
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }

    location ~ /(config|secure)/ {
        deny all;
    }
}

# HTTPS Server (disabled for testing)
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name _;
#
#     root /var/www/fusionpbx;
#     index index.php index.html index.htm;
#
#     # SSL Configuration (self-signed for development)
#     ssl_certificate /etc/ssl/certs/nginx-selfsigned.crt;
#     ssl_certificate_key /etc/ssl/private/nginx-selfsigned.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#
#     # Security headers
#     add_header X-Frame-Options "SAMEORIGIN" always;
#     add_header X-XSS-Protection "1; mode=block" always;
#     add_header X-Content-Type-Options "nosniff" always;
#     add_header Referrer-Policy "no-referrer-when-downgrade" always;
#     add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
#
#     # Gzip compression
#     gzip on;
#     gzip_vary on;
#     gzip_min_length 1024;
#     gzip_proxied expired no-cache no-store private auth;
#     gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;
#
#     # Client max body size
#     client_max_body_size 80M;
#
#     # Locations
#     location / {
#         try_files $uri $uri/ /index.php?$query_string;
#     }
#
#     location ~ \.php$ {
#         include snippets/fastcgi-php.conf;
#         fastcgi_pass unix:/var/run/php/fusionpbx-fpm.sock;
#         fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#         include fastcgi_params;
#
#         # Security
#         fastcgi_hide_header X-Powered-By;
#         fastcgi_read_timeout 300;
#     }
#
#     # Deny access to sensitive files
#     location ~ /\.ht {
#         deny all;
#     }
#
#     location ~ /\.git {
#         deny all;
#     }
#
#     location ~ /config\.php {
#         deny all;
#     }
#
#     # Static files caching
#     location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
#         expires 1y;
#         add_header Cache-Control "public, immutable";
#         access_log off;
#     }
#
#     # Recordings and sounds
#     location /recordings/ {
#         alias /usr/local/freeswitch/recordings/;
#         autoindex off;
#     }
#
#     location /sounds/ {
#         alias /usr/local/freeswitch/sounds/;
#         autoindex off;
#     }
#
#     # Error and access logs
#     access_log /var/log/nginx/fusionpbx_access.log;
#     error_log /var/log/nginx/fusionpbx_error.log;
# }
