<?xml version="1.0" encoding="utf-8"?>
<configuration name="sofia.conf" description="Sofia SIP Configuration">
  <global_settings>
    <param name="log-level" value="0"/>
    <param name="auto-restart" value="false"/>
    <param name="debug-presence" value="0"/>
  </global_settings>

  <profiles>
    <profile name="internal">
      <aliases>
        <alias name="default"/>
      </aliases>
      <gateways>
      </gateways>
      <domains>
        <domain name="all" alias="false" parse="true"/>
      </domains>
      <settings>
        <param name="debug" value="0"/>
        <param name="sip-trace" value="no"/>
        <param name="sip-capture" value="no"/>
        <param name="rfc2833-pt" value="101"/>
        <param name="sip-port" value="5060"/>
        <param name="dialplan" value="XML"/>
        <param name="context" value="default"/>
        <param name="dtmf-duration" value="2000"/>
        <param name="inbound-codec-prefs" value="OPUS,G722,PCMU,PCMA,G729"/>
        <param name="outbound-codec-prefs" value="OPUS,G722,PCMU,PCMA,G729"/>
        <param name="rtp-timer-name" value="soft"/>
        <param name="local-network-acl" value="localnet.auto"/>
        <param name="manage-presence" value="false"/>
        <param name="inbound-codec-negotiation" value="generous"/>
        <param name="nonce-ttl" value="60"/>
        <param name="auth-calls" value="false"/>
        <param name="inbound-late-negotiation" value="true"/>
        <param name="inbound-zrtp-passthru" value="true"/>
        <param name="rtp-ip" value="auto"/>
        <param name="sip-ip" value="auto"/>
        <param name="ext-rtp-ip" value="auto"/>
        <param name="ext-sip-ip" value="auto"/>
        <param name="rtp-timeout-sec" value="300"/>
        <param name="rtp-hold-timeout-sec" value="1800"/>
        <param name="enable-100rel" value="true"/>
        <param name="disable-transfer" value="true"/>
        <param name="manual-redirect" value="true"/>
        <param name="enable-compact-headers" value="true"/>
        <param name="enable-timer" value="false"/>
        <param name="minimum-session-expires" value="120"/>
        <param name="apply-inbound-acl" value="domains"/>
        <param name="record-path" value="/usr/local/freeswitch/recordings"/>
        <param name="record-template" value="${caller_id_number}.${target_domain}.${strftime(%Y-%m-%d-%H-%M-%S)}.wav"/>
      </settings>
    </profile>
  </profiles>
</configuration>
