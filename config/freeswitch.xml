<?xml version="1.0" encoding="utf-8"?>
<configuration name="freeswitch.conf" description="FreeSWITCH Configuration">
  <settings>
    <param name="colorize-console" value="true"/>
    <param name="max-sessions" value="1000"/>
    <param name="sessions-per-second" value="30"/>
    <param name="loglevel" value="info"/>
    <param name="dump-cores" value="yes"/>
    <param name="auto-restart" value="false"/>
    <param name="core-db-dsn" value="pgsql://hostaddr=127.0.0.1 dbname=freeswitch user=fusionpbx password=fusionpbx123 options='-c default_tablespace=freeswitch'"/>
  </settings>
  
  <modules>
    <!-- Core modules -->
    <load module="mod_console"/>
    <load module="mod_logfile"/>
    <load module="mod_enum"/>
    <load module="mod_cdr_csv"/>
    <load module="mod_event_socket"/>
    <load module="mod_sofia"/>
    <load module="mod_loopback"/>
    
    <!-- Dialplan modules -->
    <load module="mod_dialplan_xml"/>
    
    <!-- Codec modules -->
    <load module="mod_spandsp"/>
    <load module="mod_g723_1"/>
    <load module="mod_g729"/>
    <load module="mod_amr"/>
    <load module="mod_speex"/>
    <load module="mod_opus"/>
    
    <!-- File format modules -->
    <load module="mod_native_file"/>
    <load module="mod_sndfile"/>
    <load module="mod_tone_stream"/>
    <load module="mod_local_stream"/>
    
    <!-- Timer modules -->
    <load module="mod_timerfd"/>
    
    <!-- Applications -->
    <load module="mod_commands"/>
    <load module="mod_conference"/>
    <load module="mod_db"/>
    <load module="mod_dptools"/>
    <load module="mod_expr"/>
    <load module="mod_fifo"/>
    <load module="mod_hash"/>
    <load module="mod_voicemail"/>
    <load module="mod_directory"/>
    <load module="mod_distributor"/>
    <load module="mod_lcr"/>
    <load module="mod_esl"/>
    <load module="mod_esf"/>
    <load module="mod_fsv"/>
    
    <!-- Say modules -->
    <load module="mod_say_en"/>
    
    <!-- ASR/TTS modules -->
    <load module="mod_flite"/>
    
    <!-- Directory modules -->
    <load module="mod_ldap"/>
  </modules>
</configuration>
