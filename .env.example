# FusionPBX Environment Configuration
# Author: Finstar Team
# Version: 1.0
# Copy this file to .env and customize as needed

# Docker Image Configuration
FUSIONPBX_IMAGE=skytruongdev/fusionpbx:latest

# FusionPBX Configuration
FUSIONPBX_DOMAIN=pbx.finstar.vn
FUSIONPBX_ADMIN_USER=admin
FUSIONPBX_ADMIN_PASSWORD=Finstar@2025

# Installation Mode
FUSIONPBX_SETUP_WIZARD=false  # Disable setup wizard for auto-install
AUTO_INSTALL=true            # Enable auto-install mode

# Security Configuration
ENABLE_HTTPS=true
ENABLE_FAIL2BAN=true

# Firewall Configuration
CONFIGURE_FIREWALL=false  # Set to true to auto-configure firewall (may conflict with existing iptables)

# Database Configuration (Internal PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=fusionpbx
DB_USER=fusionpbx
DB_PASSWORD=fusionpbx

# FreeSWITCH Configuration
FREESWITCH_PASSWORD=Finstar@FS2025

# Timezone
TZ=Asia/Ho_Chi_Minh

# Environment Settings
ENVIRONMENT=production
LOG_LEVEL=info
DEBUG_MODE=false

# Network Configuration (Bridge Network Mode)
# Note: Bridge network mode for compatibility with existing services
# Port mapping to avoid conflicts:
# - HTTP: 8080 → 80
# - HTTPS: 8443 → 443
# - SIP: 5060 → 5060
# - FreeSWITCH Event Socket: 8021 → 8021
# - RTP: 10000-10100 → 10000-10100

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM

# Monitoring
MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=30s

# Development Override (uncomment for development)
# FUSIONPBX_DOMAIN=localhost
# FUSIONPBX_ADMIN_USER=finstar_admin
# DB_NAME=finstar_pbx
# DB_USER=finstar_user
# DB_PASSWORD=Finstar@DB2025
# ENVIRONMENT=development
# DEBUG_MODE=true
# ENABLE_FAIL2BAN=false
