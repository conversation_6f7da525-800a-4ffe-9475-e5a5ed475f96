#!/bin/bash

# FusionPBX Production Firewall Script
# Description: Secure iptables configuration for FusionPBX production server
# Allows SSH from public IPs with fail2ban protection

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== FusionPBX Firewall Configuration ===${NC}"
echo "Configuring secure firewall for FusionPBX production..."

# Function to log actions
log_action() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Create backup directory
mkdir -p /root/iptables-backup

# Backup current rules
log_action "Creating backup of current rules..."
iptables-save > /root/iptables-backup/pre-script-backup-$(date +%Y%m%d-%H%M%S).v4

# Save fail2ban chains before clearing
log_action "Preserving fail2ban rules..."
iptables-save | grep -E "^-A f2b-" > /tmp/f2b-rules.tmp || true
iptables-save | grep -E "^-N f2b-" > /tmp/f2b-chains.tmp || true

# Clear existing rules
log_action "Clearing existing rules..."
iptables -F INPUT
iptables -F FORWARD  
iptables -F OUTPUT

# Set default policies
log_action "Setting default policies..."
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# Allow loopback traffic
log_action "Allowing loopback traffic..."
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# Allow established and related connections
log_action "Allowing established and related connections..."
iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT

# Restore fail2ban chains and rules
if [ -s /tmp/f2b-chains.tmp ]; then
    log_action "Restoring fail2ban chains..."
    while IFS= read -r chain; do
        if [[ $chain == -N* ]]; then
            iptables $chain 2>/dev/null || true
        fi
    done < /tmp/f2b-chains.tmp
fi

if [ -s /tmp/f2b-rules.tmp ]; then
    log_action "Restoring fail2ban rules..."
    while IFS= read -r rule; do
        if [[ $rule == -A* ]]; then
            iptables $rule 2>/dev/null || log_warning "Failed to restore rule: $rule"
        fi
    done < /tmp/f2b-rules.tmp
fi

# SSH Access - Allow from anywhere (protected by fail2ban)
log_action "Configuring SSH access (public access with fail2ban protection)..."
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# HTTP/HTTPS - Web interface access
log_action "Configuring HTTP/HTTPS access..."
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# FusionPBX specific ports
log_action "Configuring FusionPBX ports..."

# SIP signaling (TCP/UDP 5060, 5080)
iptables -A INPUT -p tcp --dport 5060 -j ACCEPT
iptables -A INPUT -p udp --dport 5060 -j ACCEPT
iptables -A INPUT -p tcp --dport 5080 -j ACCEPT
iptables -A INPUT -p udp --dport 5080 -j ACCEPT

# FreeSWITCH Event Socket (8021) - Allow from anywhere for now
iptables -A INPUT -p tcp --dport 8021 -j ACCEPT

# RTP Media ports (UDP 16384-32768) - Common range for FreeSWITCH
log_action "Configuring RTP media ports..."
iptables -A INPUT -p udp --dport 16384:32768 -j ACCEPT

# Additional ports that might be needed
iptables -A INPUT -p tcp --dport 7443 -j ACCEPT  # FreeSWITCH HTTPS
iptables -A INPUT -p tcp --dport 8000 -j ACCEPT  # Additional web port

# ICMP - Allow ping but rate limit
log_action "Configuring ICMP..."
iptables -A INPUT -p icmp --icmp-type echo-request -m limit --limit 1/s --limit-burst 3 -j ACCEPT
iptables -A INPUT -p icmp --icmp-type echo-reply -j ACCEPT
iptables -A INPUT -p icmp --icmp-type destination-unreachable -j ACCEPT
iptables -A INPUT -p icmp --icmp-type time-exceeded -j ACCEPT

# Docker rules - Preserve Docker functionality
log_action "Preserving Docker rules..."
iptables -A FORWARD -j DOCKER-USER 2>/dev/null || true
iptables -A FORWARD -j DOCKER-FORWARD 2>/dev/null || true

# Drop everything else
log_action "Setting final DROP rule..."
iptables -A INPUT -j DROP

# Clean up temporary files
rm -f /tmp/f2b-rules.tmp /tmp/f2b-chains.tmp

log_action "Firewall configuration completed successfully!"
echo ""
echo -e "${GREEN}=== Current Rules Summary ===${NC}"
echo "INPUT Policy: DROP (default deny)"
echo "FORWARD Policy: DROP" 
echo "OUTPUT Policy: ACCEPT"
echo ""
echo "Allowed services:"
echo "- SSH (22) - from anywhere (protected by fail2ban)"
echo "- HTTP (80) - from anywhere"
echo "- HTTPS (443) - from anywhere" 
echo "- SIP (5060, 5080) - from anywhere"
echo "- RTP (16384-32768) - from anywhere"
echo "- FreeSWITCH ESL (8021) - from anywhere"
echo "- FreeSWITCH HTTPS (7443) - from anywhere"
echo "- Additional web (8000) - from anywhere"
echo "- ICMP ping - rate limited"
echo "- fail2ban protection - preserved"
echo ""
echo -e "${YELLOW}IMPORTANT: Test connectivity before saving rules!${NC}"
echo "To save rules: iptables-save > /etc/iptables/rules.v4"
echo "To restore backup: iptables-restore < /root/iptables-backup/pre-script-backup-*.v4"
